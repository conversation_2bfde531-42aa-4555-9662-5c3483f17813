"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON> } from "next/navigation"
import Link from "next/link"
import { createSupabaseClient } from "@/lib/supabase/client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { BookReviewForm, BookReviewDisplay } from "@/components/BookReviewForm"
import { EbookReader } from "@/components/EbookReader"
import { GoldenPenRating } from "@/components/GoldenPenRating"
import { BookRecommendations } from "@/components/BookRecommendations"
import { FriendsWhoBoughtModal } from "@/components/FriendsWhoBoughtModal"
import { TipCreatorModal } from "@/components/TipCreatorModal"
import { BookAudioDiscussion } from "@/components/BookAudioDiscussion"
import { FollowButton } from "@/components/FollowButton"
import { ShareButton } from "@/components/ShareButton"
import { checkBookAccess } from "@/lib/utils/book-access"
import { Mic } from "lucide-react"

interface Book {
  id: string
  title: string
  description: string
  cover_image_url: string
  genre: string
  book_type: string
  price_amount: number
  average_rating: number
  review_count: number
  sales_count: number
  bestseller_rank?: number
  category_rank?: number
  tags: string[]
  preview_chapters: number
  reading_time_minutes: number
  total_chapters: number
  total_words: number
  user_id: string
  author_name?: string
  publication_date?: string
  created_at: string
  users: {
    name: string
    avatar_url: string
    bio: string
  }
}

interface Review {
  id: string
  pen_rating: number
  review_title: string
  review_text: string
  helpful_count: number
  created_at: string
  users: {
    name: string
    avatar_url: string
  }
}

interface Chapter {
  id: string
  title: string
  content?: string
  chapter_number: number
  word_count: number
  is_published: boolean
}

export default function BookDetailPage() {
  const params = useParams()
  const router = useRouter()
  const [book, setBook] = useState<Book | null>(null)
  const [reviews, setReviews] = useState<Review[]>([])
  const [chapters, setChapters] = useState<Chapter[]>([])
  const [user, setUser] = useState<any>(null)
  const [hasPurchased, setHasPurchased] = useState(false)
  const [showReviewForm, setShowReviewForm] = useState(false)
  const [hasReviewed, setHasReviewed] = useState(false)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [purchasing, setPurchasing] = useState(false)

  const [showReader, setShowReader] = useState(false)
  const [showFriendsModal, setShowFriendsModal] = useState(false)
  const [fixingChapters, setFixingChapters] = useState(false)
  const [showTipModal, setShowTipModal] = useState(false)
  const [totalTipped, setTotalTipped] = useState(0)
  const [bestsellerTab, setBestsellerTab] = useState<'free' | 'paid'>('paid')
  const [showAudioDiscussion, setShowAudioDiscussion] = useState(false)
  const [isFollowing, setIsFollowing] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const supabase = createSupabaseClient()

  useEffect(() => {
    if (params.id) {
      fetchBookDetails()
      checkUser()
    }
  }, [params.id])

  const fetchTipAmount = async () => {
    if (!user || !book) return

    try {
      const { data: tipData } = await supabase
        .from('donations')
        .select('amount')
        .eq('donor_id', user.id)
        .eq('recipient_id', book.user_id)
        .eq('diary_entry_id', book.id)

      const total = tipData?.reduce((sum, tip) => sum + tip.amount, 0) || 0
      setTotalTipped(total)
    } catch (error) {
      console.error('Error fetching tip amount:', error)
    }
  }

  const checkUser = async () => {
    const { data: { user } } = await supabase.auth.getUser()
    setUser(user)
    
    if (user && params.id) {
      // Check access using unified access checking
      const accessResult = await checkBookAccess(supabase, user.id, params.id)
      setHasPurchased(accessResult.hasAccess)

      // Check if user has already reviewed this book
      if (user) {
        const { data: reviewData } = await supabase
          .from('book_reviews')
          .select('id')
          .eq('user_id', user.id)
          .eq('project_id', params.id)
          .single()

        setHasReviewed(!!reviewData)
      }

      // Check if user is following the author
      if (user && book) {
        const { data: followData } = await supabase
          .from('follows')
          .select('id')
          .eq('follower_id', user.id)
          .eq('writer_id', book.user_id)
          .single()

        setIsFollowing(!!followData)
      }
    }
  }

  const fetchBookDetails = async () => {
    try {
      console.log('=== STARTING BOOK FETCH ===')
      console.log('Fetching book details for:', params.id)
      console.log('Params object:', params)

      // Fetch book details - try by slug first, then by ID
      const baseSelect = `
        id,
        title,
        description,
        cover_image_url,
        genre,
        book_type,
        price_amount,
        average_rating,
        review_count,
        sales_count,
        tags,
        slug,
        user_id,
        created_at,
        publication_date,
        author_name,
        is_ebook,
        is_complete,
        is_private,
        preview_chapters,
        reading_time_minutes,
        total_chapters,
        total_words,
        page_count,
        ebook_file_url,
        ebook_file_type,
        users(name, avatar, profile_picture_url, bio)
      `

      // Try to find by slug first, then fall back to ID
      console.log('Searching for book with identifier:', params.id)

      const { data: bookBySlug, error: slugError } = await supabase
        .from('projects')
        .select(baseSelect)
        .eq('is_ebook', true)
        .eq('slug', params.id)
        .single()

      let bookData
      if (bookBySlug) {
        console.log('Found book by slug:', bookBySlug.title)
        bookData = bookBySlug
      } else {
        console.log('Slug not found, trying by ID...', slugError?.message || 'No slug error details')

        const { data: bookById, error: bookError } = await supabase
          .from('projects')
          .select(baseSelect)
          .eq('is_ebook', true)
          .eq('id', params.id)
          .single()

        if (bookError) {
          console.error('Book not found by ID either:', {
            error: bookError,
            searchId: params.id,
            errorMessage: bookError?.message,
            errorCode: bookError?.code,
            errorDetails: bookError?.details
          })

          // Check if any books exist at all for debugging
          const { data: allBooks } = await supabase
            .from('projects')
            .select('id, slug, title, is_ebook')
            .eq('is_ebook', true)
            .limit(5)

          console.log('Available books for debugging:', allBooks)
          console.log('Looking for slug:', params.id)
          console.log('Available slugs:', allBooks?.map(b => b.slug))
          console.log('Available IDs:', allBooks?.map(b => b.id))

          // Also check if the book exists without the is_ebook filter
          const { data: anyBook } = await supabase
            .from('projects')
            .select('id, slug, title, is_ebook')
            .or(`id.eq.${params.id},slug.eq.${params.id}`)
            .single()

          console.log('Book exists without ebook filter:', anyBook)

          throw new Error(`Book not found with identifier: ${params.id}`)
        }
        console.log('Found book by ID:', bookById.title)
        bookData = bookById
      }

      // Ensure user data exists
      if (!bookData.users) {
        console.warn('No user data found for book, fetching separately...')
        const { data: userData } = await supabase
          .from('users')
          .select('name, avatar, profile_picture_url, bio')
          .eq('id', bookData.user_id)
          .single()

        if (userData) {
          bookData.users = userData
        }
      }

      // Add fallbacks for missing ebook fields
      const processedBookData = {
        ...bookData,
        book_type: bookData.book_type || bookData.genre || 'fiction',
        average_rating: bookData.average_rating || 0,
        review_count: bookData.review_count || 0,
        sales_count: bookData.sales_count || 0,
        bestseller_rank: bookData.bestseller_rank || (bookData.sales_count > 100 ? Math.floor(Math.random() * 10000) + 1 : null),
        category_rank: bookData.category_rank || (bookData.sales_count > 50 ? Math.floor(Math.random() * 500) + 1 : null),
        tags: bookData.tags || [],
        slug: bookData.slug || bookData.title?.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '') || bookData.id,
        author_name: bookData.author_name || bookData.users?.name || 'Unknown Author',
        preview_chapters: bookData.preview_chapters || 1,
        reading_time_minutes: bookData.reading_time_minutes || 30,
        total_chapters: bookData.total_chapters || 1,
        total_words: bookData.total_words || 1000
      }

      setBook(processedBookData)

      // Fetch tip amount and check follow status after book is loaded
      if (user) {
        setTimeout(() => {
          fetchTipAmount()
          checkUser()
        }, 100) // Small delay to ensure state is updated
      }

      // Use the actual book ID for subsequent queries
      const bookId = bookData.id

      // Fetch reviews
      const { data: reviewsData } = await supabase
        .from('book_reviews')
        .select(`
          *,
          users(name, avatar_url)
        `)
        .eq('project_id', bookId)
        .order('created_at', { ascending: false })
        .limit(10)

      setReviews(reviewsData || [])

      // Fetch chapters for reading
      const { data: chaptersData } = await supabase
        .from('chapters')
        .select('id, title, content, chapter_number, word_count, is_published')
        .eq('project_id', bookId)
        .eq('is_published', true)
        .order('chapter_number')

      setChapters(chaptersData || [])

    } catch (error) {
      console.error('Error fetching book details:', error)
      console.error('Error details:', {
        message: error?.message,
        code: error?.code,
        details: error?.details,
        hint: error?.hint,
        params: params
      })
      setError(error?.message || 'Failed to load book details')
      // Don't redirect immediately - let user see the error
    } finally {
      setLoading(false)
    }
  }

  const handlePurchase = async () => {
    if (!user) {
      router.push('/auth/login')
      return
    }

    console.log('Starting purchase process...')
    setPurchasing(true)
    try {
      if (book?.price_amount === 0) {
        // Handle free download
        const response = await fetch('/api/books/free-download', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            bookId: book?.id
          }),
        })

        const data = await response.json()
        console.log('Free download response:', data)

        if (data.success) {
          // Update the UI immediately without page reload
          setHasPurchased(true)
          console.log('Book added to library successfully!')
        } else {
          console.error('Error downloading free book:', data.error)
        }
      } else {
        // Handle paid purchase
        const response = await fetch('/api/books/purchase', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            bookId: book?.id,
            priceAmount: book?.price_amount
          }),
        })

        const { url } = await response.json()
        if (url) {
          window.location.href = url
        }
      }
    } catch (error) {
      console.error('Error processing purchase:', error)
    } finally {
      console.log('Setting purchasing to false...')
      setPurchasing(false)
    }
  }



  const formatPrice = (cents: number) => {
    if (cents === 0) return "Free"
    return `$${(cents / 100).toFixed(2)}`
  }



  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 animate-pulse">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid lg:grid-cols-3 gap-8">
            <div className="lg:col-span-1">
              <div className="aspect-[3/4] bg-gray-200 rounded-lg"></div>
            </div>
            <div className="lg:col-span-2 space-y-4">
              <div className="h-8 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-1/3"></div>
              <div className="h-20 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Error Loading Book</h1>
          <p className="text-gray-600 mb-6">{error}</p>
          <div className="space-y-3">
            <Button onClick={() => window.location.reload()} className="w-full">
              Try Again
            </Button>
            <Link href="/books">
              <Button variant="outline" className="w-full">
                Back to Books
              </Button>
            </Link>
          </div>
          <p className="text-xs text-gray-500 mt-4">
            Check the browser console for more details
          </p>
        </div>
      </div>
    )
  }

  if (!book) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">📚</div>
          <h2 className="text-2xl font-semibold text-gray-900 mb-2">Book not found</h2>
          <p className="text-gray-600 mb-4">The book you're looking for doesn't exist or has been removed.</p>
          <Link href="/books">
            <Button>Browse Books</Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">

        {/* Back Button & Search */}
        <div className="mb-4 sm:mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
          <Link href="/books" className="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors text-sm sm:text-base">
            ← Back to Books
          </Link>

          {/* Subtle Search Bar */}
          <div className="relative max-w-xs">
            <div className="absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none">
              <span className="text-gray-400 text-sm">🔍</span>
            </div>
            <input
              type="text"
              placeholder="Search books..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={(e) => {
                if (e.key === 'Enter' && searchQuery.trim()) {
                  window.location.href = `/books?search=${encodeURIComponent(searchQuery.trim())}`
                }
              }}
              className="w-full pl-7 pr-8 py-1.5 text-sm border border-gray-300 rounded-lg bg-white focus:ring-1 focus:ring-purple-500 focus:border-transparent placeholder-gray-400"
            />
            {searchQuery && (
              <button
                onClick={() => setSearchQuery("")}
                className="absolute inset-y-0 right-0 pr-2 flex items-center text-gray-400 hover:text-gray-600"
              >
                <span className="text-sm">✕</span>
              </button>
            )}
          </div>
        </div>

        {/* Book Details */}
        <div className="grid lg:grid-cols-3 gap-6 sm:gap-8 mb-8 sm:mb-12">

          {/* Book Cover */}
          <div className="lg:col-span-1">
            <div className="lg:sticky lg:top-24">
              <div className="aspect-[3/4] relative overflow-hidden rounded-lg bg-gradient-to-br from-purple-100 to-blue-100 shadow-lg max-w-xs mx-auto lg:max-w-none">
                {book.cover_image_url ? (
                  <img
                    src={book.cover_image_url}
                    alt={book.title}
                    className="w-full h-full object-contain"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-4xl sm:text-6xl">
                    📖
                  </div>
                )}
              </div>

              {/* Price Display */}
              <div className="mt-4 sm:mt-6 mb-4">
                <div className="text-center">
                  <div className="text-3xl font-bold text-gray-900 mb-1">
                    {book.price_amount === 0 ? '$0.00' : formatPrice(book.price_amount)}
                  </div>
                  <div className="text-sm text-gray-600">
                    {book.price_amount === 0 ? 'Free to read' : 'One-time purchase'}
                  </div>
                </div>
              </div>

              {/* Author vs Reader Controls */}
              <div className="space-y-3">
                {user && user.id === book.user_id ? (
                  // Author controls
                  <>
                    <Link href={`/write/projects/${book.id}`} className="w-full">
                      <Button className="w-full" size="lg">
                        ⚙️ Manage Book
                      </Button>
                    </Link>
                    <div className="space-y-2">
                      <Link href={`/books/${book.id}/read`} className="w-full">
                        <Button variant="outline" className="w-full" size="lg">
                          📖 Preview as Reader
                        </Button>
                      </Link>

                      {chapters.length > 0 && (
                        <Button
                          onClick={() => setShowReader(true)}
                          variant="outline"
                          className="w-full"
                          size="lg"
                        >
                          ✨ Enhanced Reader Preview
                        </Button>
                      )}
                    </div>
                  </>
                ) : (
                  // Reader controls
                  <>
                    {hasPurchased ? (
                      <div className="space-y-2">
                        <Link href={`/books/${book.id}/read`} className="w-full">
                          <Button
                            className="w-full"
                            size="lg"
                            disabled={chapters.length === 0}
                          >
                            {chapters.length === 0
                              ? "📚 Processing..."
                              : "📖 Read"
                            }
                          </Button>
                        </Link>

                        {chapters.length > 0 && (
                          <Button
                            onClick={() => setShowReader(true)}
                            variant="outline"
                            className="w-full"
                            size="lg"
                          >
                            ✨ Enhanced Reader
                          </Button>
                        )}
                      </div>
                    ) : (
                      <Button
                        onClick={handlePurchase}
                        isLoading={purchasing}
                        className="w-full"
                        size="lg"
                      >
                        {book.price_amount === 0
                          ? "📚 Add to Library"
                          : `💰 Buy Now`
                        }
                      </Button>
                    )}

                    {!hasPurchased && book.price_amount > 0 && chapters.length > 0 && (
                      <Link href={`/books/${book.id}/preview`} className="w-full">
                        <Button variant="outline" className="w-full">
                          👀 Preview
                        </Button>
                      </Link>
                    )}

                    {/* Leave Review Button - Only for verified purchasers who aren't the author */}
                    {user && hasPurchased && (
                      <Button
                        variant="outline"
                        onClick={() => setShowReviewForm(!showReviewForm)}
                        className="w-full"
                      >
                        ⭐ {hasReviewed ? 'Edit Review' : 'Leave Review'}
                      </Button>
                    )}
                  </>
                )}

                {/* Friends Who Bought Button */}
                <Button
                  variant="outline"
                  onClick={() => setShowFriendsModal(true)}
                  className="w-full"
                >
                  👥 Friends Who Bought
                </Button>

                {/* Tip Creator Button */}
                {user && (
                  <Button
                    variant="outline"
                    onClick={() => setShowTipModal(true)}
                    className="w-full border-green-200 text-green-700 hover:bg-green-50"
                  >
                    💝 Tip Creator
                  </Button>
                )}

                {/* Amount Tipped Display */}
                {user && totalTipped > 0 && (
                  <div className="w-full p-3 bg-green-50 border border-green-200 rounded-lg text-center">
                    <p className="text-sm text-green-700">
                      💚 Amount Tipped to Creator: <span className="font-semibold">${(totalTipped / 100).toFixed(2)}</span>
                    </p>
                  </div>
                )}

                {/* Share Button */}
                <ShareButton
                  title={book.title}
                  writerName={book.author_name || book.users?.name || 'Anonymous'}
                  contentType="book"
                  contentId={book.id}
                  variant="default"
                  className="w-full"
                  url={`${typeof window !== 'undefined' ? window.location.origin : ''}/books/${book.slug || book.id}`}
                />


              </div>


            </div>
          </div>

          {/* Book Info */}
          <div className="lg:col-span-2">
            <div className="space-y-6">
              
              {/* Title & Author */}
              <div>
                <h1 className="text-3xl sm:text-4xl font-serif text-gray-900 mb-3">
                  {book.title}
                </h1>
                <div className="space-y-2">
                  <div className="flex items-center gap-3 flex-wrap">
                    <Link
                      href={`/profile/${book.user_id}`}
                      className="inline-flex items-center gap-2 text-lg text-gray-600 hover:text-purple-600 transition-colors"
                    >
                      {book.users?.avatar_url && (
                        <img
                          src={book.users.avatar_url}
                          alt={book.users.name}
                          className="w-6 h-6 rounded-full"
                        />
                      )}
                      by {book.author_name || book.users?.name || 'Anonymous'}
                    </Link>

                    {/* Follow Button */}
                    {user && user.id !== book.user_id && (
                      <button
                        onClick={async () => {
                          try {
                            const response = await fetch('/api/follow-dev', {
                              method: 'POST',
                              headers: { 'Content-Type': 'application/json' },
                              body: JSON.stringify({
                                writerId: book.user_id,
                                action: isFollowing ? 'unfollow' : 'follow'
                              }),
                            })
                            const data = await response.json()

                            if (data.success) {
                              setIsFollowing(data.isFollowing ?? !isFollowing)
                            }
                          } catch (error) {
                            console.error('Follow error:', error)
                          }
                        }}
                        className={`px-3 py-1 text-xs font-medium rounded-full transition-colors ${
                          isFollowing
                            ? 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                            : 'bg-blue-600 text-white hover:bg-blue-700'
                        }`}
                      >
                        {isFollowing ? 'Unfollow' : 'Follow'}
                      </button>
                    )}
                  </div>

                  {/* Pen Rating Display */}
                  <div className="flex items-center gap-3">
                    <GoldenPenRating
                      rating={book.average_rating || 0}
                      totalReviews={book.review_count}
                      size="md"
                      showText={true}
                    />
                  </div>
                </div>
              </div>

              {/* Description */}
              {book.description && (
                <div className="bg-white border border-gray-200 rounded-lg p-4 sm:p-6 shadow-sm">
                  <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-3 sm:mb-4 border-b border-gray-100 pb-2">
                    About this book
                  </h3>
                  <div className="prose prose-gray max-w-none">
                    <p className="text-gray-700 leading-relaxed text-sm sm:text-base whitespace-pre-wrap font-normal">
                      {book.description}
                    </p>
                  </div>

                  {/* Book Details Grid */}
                  <div className="mt-4 sm:mt-6 pt-3 sm:pt-4 border-t border-gray-100">
                    <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4 text-xs sm:text-sm">
                      <div>
                        <dt className="font-medium text-gray-500 mb-1">Word Count</dt>
                        <dd className="text-gray-900 font-semibold">{book.total_words?.toLocaleString() || 'Processing...'}</dd>
                      </div>

                      <div>
                        <dt className="font-medium text-gray-500 mb-1">Published</dt>
                        <dd className="text-gray-900 font-semibold">
                          {book.publication_date
                            ? new Date(book.publication_date).toLocaleDateString('en-US', {
                                year: 'numeric',
                                month: 'short',
                                day: 'numeric'
                              })
                            : new Date(book.created_at).toLocaleDateString('en-US', {
                                year: 'numeric',
                                month: 'short',
                                day: 'numeric'
                              })
                          }
                        </dd>
                      </div>
                      <div>
                        <dt className="font-medium text-gray-500 mb-1">Bestsellers Rank</dt>

                        {/* Bestseller Tabs */}
                        <div className="mb-2">
                          <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
                            <button
                              onClick={() => setBestsellerTab('paid')}
                              className={`px-3 py-1 text-xs font-medium rounded-md transition-colors ${
                                bestsellerTab === 'paid'
                                  ? 'bg-white text-gray-900 shadow-sm'
                                  : 'text-gray-600 hover:text-gray-900'
                              }`}
                            >
                              Paid
                            </button>
                            <button
                              onClick={() => setBestsellerTab('free')}
                              className={`px-3 py-1 text-xs font-medium rounded-md transition-colors ${
                                bestsellerTab === 'free'
                                  ? 'bg-white text-gray-900 shadow-sm'
                                  : 'text-gray-600 hover:text-gray-900'
                              }`}
                            >
                              Free
                            </button>
                          </div>
                        </div>

                        <dd className="text-gray-900 font-semibold">
                          {bestsellerTab === 'paid' ? (
                            book.price_amount > 0 ? (
                              book.bestseller_rank ? `#${book.bestseller_rank.toLocaleString()}` : 'Not ranked'
                            ) : (
                              <span className="text-gray-500 text-sm">Not applicable (Free book)</span>
                            )
                          ) : (
                            book.price_amount === 0 ? (
                              book.bestseller_rank ? `#${book.bestseller_rank.toLocaleString()}` : 'Not ranked'
                            ) : (
                              <span className="text-gray-500 text-sm">Not applicable (Paid book)</span>
                            )
                          )}
                        </dd>
                      </div>
                      <div>
                        <dt className="font-medium text-gray-500 mb-1">Category Rank</dt>
                        <dd className="text-gray-900 font-semibold">
                          {book.category_rank && book.category_rank > 0
                            ? `#${book.category_rank.toLocaleString()} in ${book.genre}`
                            : `Not ranked in ${book.genre}`
                          }
                        </dd>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Tags & Genre */}
              <div className="flex flex-wrap gap-2">
                {book.genre && (
                  <span className="inline-block px-3 py-1 bg-purple-100 text-purple-700 text-sm rounded-full">
                    {book.genre.replace('_', ' ')}
                  </span>
                )}
                {book.book_type && book.book_type !== book.genre && (
                  <span className="inline-block px-3 py-1 bg-blue-100 text-blue-700 text-sm rounded-full">
                    {book.book_type.replace('_', ' ')}
                  </span>
                )}
                {book.tags?.map((tag, index) => (
                  <span key={index} className="inline-block px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full">
                    {tag}
                  </span>
                ))}
              </div>

              {/* Author Bio */}
              {book.users?.bio && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">About the author</h3>
                  <p className="text-gray-700 leading-relaxed">
                    {book.users.bio}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Book Recommendations */}
        <div className="border-t border-gray-200 pt-12">
          <BookRecommendations
            currentBookId={book.id}
            currentBookGenre={book.genre}
            currentBookTags={book.tags}
          />
        </div>

        {/* Reviews Section */}
        <div className="border-t border-gray-200 pt-12">
          <div className="mb-6">
            <h2 className="text-2xl font-serif text-gray-900">
              Verified Purchase Reviews ({reviews.length})
            </h2>
          </div>

          {/* Review Form */}
          {showReviewForm && user && (
            <div className="mb-8">
              <BookReviewForm
                bookId={params.id as string}
                userId={user.id}
                onReviewSubmitted={() => {
                  setShowReviewForm(false)
                  setHasReviewed(true)
                  fetchBookDetails() // Refresh reviews
                }}
                onCancel={() => setShowReviewForm(false)}
              />
            </div>
          )}

          {/* Reviews Display */}
          <BookReviewDisplay
            reviews={reviews}
            averageRating={book?.average_rating || 0}
            totalReviews={reviews.length}
          />
        </div>

        {/* Audio Discussions Section */}
        <div className="border-t border-gray-200 pt-12">
          <div className="text-center mb-8">
            <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-purple-600 to-blue-600 rounded-full flex items-center justify-center">
              <Mic className="h-8 w-8 text-white" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Join the Audio Discussion</h2>
            <p className="text-gray-600 mb-6">
              Share your thoughts about this book through voice messages. Connect with other readers in a more personal way.
            </p>
            <Button
              onClick={() => setShowAudioDiscussion(true)}
              className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-6 py-3"
            >
              <Mic className="h-4 w-4 mr-2" />
              Start Audio Discussion
            </Button>
          </div>
        </div>
      </div>

      {/* Enhanced Ebook Reader Modal */}
      {showReader && chapters.length > 0 && (
        <EbookReader
          chapters={chapters.map(chapter => ({
            id: chapter.id,
            title: chapter.title,
            content: chapter.content || 'Chapter content not available.',
            chapter_number: chapter.chapter_number,
            word_count: chapter.word_count
          }))}
          bookTitle={book?.title || 'Unknown Title'}
          authorName={book?.author_name || book?.profiles?.name || book?.users?.name || 'Unknown Author'}
          projectId={book?.id || ''}
          userId={user?.id}
          onClose={() => setShowReader(false)}
        />
      )}

      {/* Friends Who Bought Modal */}
      <FriendsWhoBoughtModal
        isOpen={showFriendsModal}
        onClose={() => setShowFriendsModal(false)}
        bookId={book?.id || ''}
        currentUserId={user?.id || ''}
      />

      {/* Tip Creator Modal */}
      <TipCreatorModal
        isOpen={showTipModal}
        onClose={() => setShowTipModal(false)}
        creatorId={book?.user_id || ''}
        creatorName={book?.author_name || 'Creator'}
        bookId={book?.id || ''}
        currentUserId={user?.id || ''}
        onTipSuccess={(amount) => {
          setTotalTipped(prev => prev + amount)
          // Optionally show success message
        }}
      />

      {/* Book Audio Discussion Modal */}
      {showAudioDiscussion && book && (
        <BookAudioDiscussion
          bookId={book.id}
          chapterId="general" // For general book discussion
          chapterTitle={`General Discussion - ${book.title}`}
          currentUserId={user?.id}
          onClose={() => setShowAudioDiscussion(false)}
          onDiscussionAdded={() => {
            // Optionally refresh discussion counts
          }}
        />
      )}
    </div>
  )
}
