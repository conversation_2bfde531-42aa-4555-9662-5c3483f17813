'use client'

import { useState, useEffect, useRef } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'
import Image from 'next/image'
import { X, Send, Image as ImageIcon, User } from 'lucide-react'

interface Message {
  id: string
  sender_id: string
  recipient_id: string
  subject: string
  body: string
  photo_url?: string
  message_type: 'text' | 'photo' | 'text_with_photo'
  created_at: string
  sender: {
    id: string
    name: string
    profile_picture_url?: string
    avatar?: string
  }
  recipient: {
    id: string
    name: string
    profile_picture_url?: string
    avatar?: string
  }
}

interface MessageThreadModalProps {
  isOpen: boolean
  onClose: () => void
  otherUserId: string
  otherUserName: string
  otherUserAvatar?: string
  currentUserId: string
}

export function MessageThreadModal({ 
  isOpen, 
  onClose, 
  otherUserId, 
  otherUserName, 
  otherUserAvatar,
  currentUserId 
}: MessageThreadModalProps) {
  const [messages, setMessages] = useState<Message[]>([])
  const [loading, setLoading] = useState(true)
  const [newMessage, setNewMessage] = useState('')
  const [sending, setSending] = useState(false)
  const [selectedPhoto, setSelectedPhoto] = useState<File | null>(null)
  const [photoPreview, setPhotoPreview] = useState<string | null>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const supabase = createSupabaseClient()

  // Format time ago utility function
  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

    if (diffInHours < 1) return 'Just now'
    if (diffInHours < 24) return `${diffInHours}h ago`
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`
    return date.toLocaleDateString()
  }

  useEffect(() => {
    if (isOpen && otherUserId) {
      fetchMessages()
    }
  }, [isOpen, otherUserId])

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const fetchMessages = async () => {
    try {
      const { data, error } = await supabase
        .from('direct_messages')
        .select(`
          *,
          sender:users!sender_id(id, name, profile_picture_url, avatar),
          recipient:users!recipient_id(id, name, profile_picture_url, avatar)
        `)
        .or(`and(sender_id.eq.${currentUserId},recipient_id.eq.${otherUserId}),and(sender_id.eq.${otherUserId},recipient_id.eq.${currentUserId})`)
        .order('created_at', { ascending: true })

      if (error) throw error
      setMessages(data || [])

      // Mark messages as read
      await supabase
        .from('direct_messages')
        .update({ read_at: new Date().toISOString() })
        .eq('sender_id', otherUserId)
        .eq('recipient_id', currentUserId)
        .is('read_at', null)

    } catch (error) {
      console.error('Error fetching messages:', error)
    } finally {
      setLoading(false)
    }
  }

  const handlePhotoSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setSelectedPhoto(file)
      const reader = new FileReader()
      reader.onload = (e) => setPhotoPreview(e.target?.result as string)
      reader.readAsDataURL(file)
    }
  }

  const uploadPhoto = async (file: File): Promise<string | null> => {
    try {
      const fileExt = file.name.split('.').pop()
      const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`
      const filePath = `message-photos/${fileName}`

      const { error: uploadError } = await supabase.storage
        .from('photos')
        .upload(filePath, file)

      if (uploadError) throw uploadError

      const { data } = supabase.storage
        .from('photos')
        .getPublicUrl(filePath)

      return data.publicUrl
    } catch (error) {
      console.error('Error uploading photo:', error)
      return null
    }
  }

  const handleSend = async () => {
    if ((!newMessage.trim() && !selectedPhoto) || sending) return

    setSending(true)
    try {
      let photoUrl = null
      if (selectedPhoto) {
        photoUrl = await uploadPhoto(selectedPhoto)
        if (!photoUrl) {
          alert('Failed to upload photo')
          return
        }
      }

      let messageType = 'text'
      if (photoUrl && newMessage.trim()) {
        messageType = 'text_with_photo'
      } else if (photoUrl) {
        messageType = 'photo'
      }

      const { data, error } = await supabase
        .from('direct_messages')
        .insert({
          sender_id: currentUserId,
          recipient_id: otherUserId,
          subject: 'Reply',
          body: newMessage.trim() || null,
          photo_url: photoUrl,
          message_type: messageType
        })
        .select(`
          *,
          sender:users!sender_id(id, name, profile_picture_url, avatar),
          recipient:users!recipient_id(id, name, profile_picture_url, avatar)
        `)
        .single()

      if (error) throw error

      setMessages(prev => [...prev, data])
      setNewMessage('')
      setSelectedPhoto(null)
      setPhotoPreview(null)

    } catch (error) {
      console.error('Error sending message:', error)
      alert('Failed to send message')
    } finally {
      setSending(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSend()
    }
  }

  if (!isOpen) return null

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/50 z-[60]"
        onClick={onClose}
      />

      {/* Modal */}
      <div className="fixed inset-0 z-[60] flex items-center justify-center p-4">
        <div className="bg-white rounded-2xl shadow-2xl w-full max-w-md h-[80vh] flex flex-col overflow-hidden">
          
          {/* Header */}
          <div className="p-4 border-b border-gray-200 flex items-center gap-3 bg-white">
            <div className="flex items-center gap-3 flex-1">
              {otherUserAvatar ? (
                <Image
                  src={otherUserAvatar}
                  alt={otherUserName}
                  width={40}
                  height={40}
                  className="w-10 h-10 rounded-full object-cover"
                />
              ) : (
                <div className="w-10 h-10 rounded-full bg-gradient-to-br from-purple-500 to-blue-600 flex items-center justify-center text-white font-medium">
                  <User className="w-5 h-5" />
                </div>
              )}
              <div>
                <h3 className="font-semibold text-gray-900">{otherUserName}</h3>
                <p className="text-xs text-gray-500">Direct Message</p>
              </div>
            </div>
            <button 
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <X className="w-5 h-5 text-gray-500" />
            </button>
          </div>

          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-400"></div>
              </div>
            ) : messages.length > 0 ? (
              messages.map((message) => {
                const isFromMe = message.sender_id === currentUserId
                return (
                  <div key={message.id} className={`flex ${isFromMe ? 'justify-end' : 'justify-start'}`}>
                    <div className={`max-w-[80%] ${isFromMe ? 'order-2' : 'order-1'}`}>
                      <div className={`rounded-2xl px-4 py-2 ${
                        isFromMe 
                          ? 'bg-blue-500 text-white' 
                          : 'bg-gray-100 text-gray-900'
                      }`}>
                        {message.photo_url && (
                          <div className="mb-2">
                            <Image
                              src={message.photo_url}
                              alt="Shared photo"
                              width={200}
                              height={200}
                              className="rounded-lg max-w-full h-auto"
                            />
                          </div>
                        )}
                        {message.body && (
                          <p className="text-sm leading-relaxed">{message.body}</p>
                        )}
                      </div>
                      <p className={`text-xs text-gray-500 mt-1 ${isFromMe ? 'text-right' : 'text-left'}`}>
                        {formatTimeAgo(message.created_at)}
                      </p>
                    </div>
                  </div>
                )
              })
            ) : (
              <div className="text-center py-8 text-gray-500">
                <p>No messages yet. Start the conversation!</p>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Photo Preview */}
          {photoPreview && (
            <div className="p-4 border-t border-gray-200 bg-gray-50">
              <div className="relative inline-block">
                <Image
                  src={photoPreview}
                  alt="Preview"
                  width={80}
                  height={80}
                  className="w-20 h-20 rounded-lg object-cover"
                />
                <button
                  onClick={() => {
                    setSelectedPhoto(null)
                    setPhotoPreview(null)
                  }}
                  className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center text-xs"
                >
                  ×
                </button>
              </div>
            </div>
          )}

          {/* Input */}
          <div className="p-4 border-t border-gray-200 bg-white">
            <div className="flex items-end gap-2">
              <input
                type="file"
                accept="image/*"
                onChange={handlePhotoSelect}
                className="hidden"
                id="photo-upload"
              />
              <label
                htmlFor="photo-upload"
                className="p-2 text-gray-500 hover:text-gray-700 cursor-pointer"
              >
                <ImageIcon className="w-5 h-5" />
              </label>
              
              <div className="flex-1">
                <textarea
                  value={newMessage}
                  onChange={(e) => {
                    setNewMessage(e.target.value)
                    // Auto-resize textarea
                    e.target.style.height = 'auto'
                    e.target.style.height = Math.min(e.target.scrollHeight, 120) + 'px'
                  }}
                  onKeyPress={handleKeyPress}
                  placeholder="Type a message..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none min-h-[40px] max-h-[120px]"
                  rows={1}
                  maxLength={1000}
                />
              </div>
              
              <button
                onClick={handleSend}
                disabled={(!newMessage.trim() && !selectedPhoto) || sending}
                className="p-2 bg-blue-500 text-white rounded-full hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {sending ? (
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                ) : (
                  <Send className="w-5 h-5" />
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
